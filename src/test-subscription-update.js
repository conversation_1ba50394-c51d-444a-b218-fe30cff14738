/**
 * 测试订阅状态更新功能
 */

const { updateUserSubscription } = require('./src/models/user');

async function testSubscriptionUpdate() {
  try {
    console.log('Testing subscription update...');
    
    // 测试用户UUID（从日志中获取的实际用户）
    const user_uuid = 'ff7955b2-b452-4184-9f99-45719e44ea2e';
    
    // 模拟升级到Professional计划
    const expires_at = new Date();
    expires_at.setMonth(expires_at.getMonth() + 1); // 1个月后过期
    
    const updatedUser = await updateUserSubscription(
      user_uuid,
      'professional',
      'active',
      expires_at,
      'test_stripe_session_id'
    );
    
    console.log('Subscription updated successfully:', {
      user_uuid: updatedUser.uuid,
      subscription_plan: updatedUser.subscription_plan,
      subscription_status: updatedUser.subscription_status,
      subscription_expires_at: updatedUser.subscription_expires_at,
      subscription_stripe_id: updatedUser.subscription_stripe_id,
    });
    
    return updatedUser;
    
  } catch (error) {
    console.error('Test failed:', error);
    throw error;
  }
}

// 运行测试
testSubscriptionUpdate()
  .then(() => {
    console.log('✅ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
