/**
 * 测试订阅状态更新的API端点
 */

import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { updateUserSubscription, findUserByUuid } from "@/models/user";
import { getUserUuid } from "@/services/user";

export async function POST(req: NextRequest) {
  try {
    const { plan, months } = await req.json();
    
    // 获取当前用户
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // 验证参数
    if (!plan || !months) {
      return respErr("Missing plan or months parameter");
    }

    if (!["professional", "commercial"].includes(plan)) {
      return respErr("Invalid plan. Must be 'professional' or 'commercial'");
    }

    // 获取用户当前状态
    const currentUser = await findUserByUuid(user_uuid);
    if (!currentUser) {
      return respErr("User not found");
    }

    console.log("Current user subscription:", {
      user_uuid,
      current_plan: currentUser.subscription_plan,
      current_status: currentUser.subscription_status,
      current_expires_at: currentUser.subscription_expires_at,
    });

    // 计算过期时间
    const now = new Date();
    const expires_at = new Date(now);
    expires_at.setMonth(expires_at.getMonth() + parseInt(months));

    // 更新订阅状态
    const updatedUser = await updateUserSubscription(
      user_uuid,
      plan,
      "active",
      expires_at,
      `test_stripe_session_${Date.now()}`
    );

    console.log("Subscription updated successfully:", {
      user_uuid: updatedUser.uuid,
      subscription_plan: updatedUser.subscription_plan,
      subscription_status: updatedUser.subscription_status,
      subscription_expires_at: updatedUser.subscription_expires_at,
      subscription_stripe_id: updatedUser.subscription_stripe_id,
    });

    return respData({
      success: true,
      message: `Subscription updated to ${plan} for ${months} months`,
      user: {
        uuid: updatedUser.uuid,
        subscription_plan: updatedUser.subscription_plan,
        subscription_status: updatedUser.subscription_status,
        subscription_expires_at: updatedUser.subscription_expires_at,
        subscription_stripe_id: updatedUser.subscription_stripe_id,
      }
    });

  } catch (error) {
    console.error("Test subscription update failed:", error);
    return respErr("Failed to update subscription");
  }
}
