import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { getUserGenerations } from "@/models/music-generation";
import { findTrackByGenerationUuid, insertTrack } from "@/models/track";
import { TrackCreationService } from "@/services/track-creation-service";
import { generateTrackSlug } from "@/lib/track-slug";
import { randomUUID } from "crypto";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.uuid) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0");
    const status = searchParams.get("status");

    const generations = await getUserGenerations(
      session.user.uuid,
      limit,
      offset,
      status as any
    );

    // 为每个已完成的生成记录检查并创建对应的 track 记录
    const generationsWithTracks = await Promise.all(
      generations.map(async (generation) => {
        if (generation.status === "completed") {
          // 检查是否已有对应的 track 记录
          let track = await findTrackByGenerationUuid(generation.uuid);

          if (!track) {
            // 如果没有 track 记录，创建一个占位符track（不处理水印）
            const trackUuid = randomUUID();
            const slug = generateTrackSlug({
              prompt: generation.prompt,
              bpm: generation.bpm || undefined,
              uuid: trackUuid,
              style: generation.style || undefined
            });

            track = await insertTrack({
              uuid: trackUuid,
              user_uuid: generation.user_uuid,
              generation_uuid: generation.uuid,
              prompt: generation.prompt,
              title: generation.prompt.substring(0, 100),
              slug: slug,
              style: generation.style,
              mood: generation.mood,
              bpm: generation.bpm,
              // New fields for API compatibility
              genre: generation.genre,
              instrument: generation.instrument,
              theme: generation.theme,
              file_url: "https://example.com/placeholder.mp3", // 临时占位符
              file_size: 0,
              file_format: "mp3",
              duration: generation.duration,
              download_count: 0,
              is_public: true, // 默认公开
              // Premium字段使用默认值，完成时会更新
              created_at: new Date(),
              updated_at: new Date(),
            });
          }

          // 将 track 信息合并到 generation 中
          return {
            ...generation,
            track: track
          };
        }

        return generation;
      })
    );

    return NextResponse.json({
      generations: generationsWithTracks,
      success: true
    });
  } catch (error) {
    console.error("Failed to get user generations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
