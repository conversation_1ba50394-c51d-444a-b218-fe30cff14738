import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserCredits, decreaseCredits, CreditsTransType } from "@/services/credit";
import { insertMusicGeneration } from "@/models/music-generation";
import { getUuid } from "@/lib/hash";
import { GenerateMusicRequest, GenerateMusicResponse } from "@/types/music-api";
import { MusicGenerationRequest } from "@/types/music";

// 导入状态更新器以确保它在服务器启动时运行
import "@/services/music-status-updater";

// Music generation costs
const GENERATION_COSTS = {
  15: 1,  // 15 seconds = 1 credit
  30: 2,  // 30 seconds = 2 credits
  60: 3,  // 60 seconds = 3 credits
};

export async function POST(req: Request) {
  try {
    const body: GenerateMusicRequest = await req.json();

    // 获取默认提供商，优先级：volcano > mubert > suno
    const getDefaultProvider = () => {
      if (process.env.VOLCANO_ACCESS_KEY_ID && process.env.VOLCANO_SECRET_ACCESS_KEY) {
        return "volcano";
      }
      if (process.env.MUBERT_API_KEY) {
        return "mubert";
      }
      if (process.env.SUNO_API_KEY) {
        return "suno";
      }
      return "volcano"; // 默认返回 volcano
    };

    const { prompt, style, mood, bpm, duration, genres, instruments, themes, options } = body;

    // 不从请求中获取 provider，始终使用系统配置的默认提供商
    const provider = getDefaultProvider();

    // Validate required parameters
    if (!prompt || !duration) {
      return respErr("Missing required parameters: prompt and duration");
    }

    // Validate duration
    if (![15, 30, 60].includes(duration)) {
      return respErr("Duration must be 15, 30, or 60 seconds");
    }

    // Validate BPM if provided
    if (bpm && (bpm < 60 || bpm > 200)) {
      return respErr("BPM must be between 60 and 200");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Check user credits
    const userCredits = await getUserCredits(user_uuid);
    const requiredCredits = GENERATION_COSTS[duration as keyof typeof GENERATION_COSTS];
    
    if (userCredits.left_credits < requiredCredits) {
      return respErr(`Insufficient credits. Required: ${requiredCredits}, Available: ${userCredits.left_credits}`);
    }

    // Generate unique UUID for this generation request
    const generation_uuid = getUuid();

    // Create music generation record
    const generationData = {
      uuid: generation_uuid,
      user_uuid,
      prompt,
      style: style || null,
      mood: mood || null,
      bpm: bpm || null,
      duration,
      // New fields for API compatibility
      genre: genres && genres.length > 0 ? genres : null,
      instrument: instruments && instruments.length > 0 ? instruments : null,
      theme: themes && themes.length > 0 ? themes : null,
      provider, // 使用系统选择的提供商
      status: "pending" as const,
      created_at: new Date(),
    };

    const generation = await insertMusicGeneration(generationData);
    if (!generation) {
      return respErr("Failed to create generation request");
    }

    // Deduct credits
    try {
      await decreaseCredits({
        user_uuid,
        trans_type: CreditsTransType.Ping, // TODO: Create MusicGeneration type
        credits: requiredCredits,
      });
    } catch (error) {
      console.error("Failed to deduct credits:", error);
      return respErr("Failed to process payment");
    }

    // Queue the actual music generation job
    try {
      const { MusicGenerationService } = await import("@/services/music-generation");
      const { MusicProviderFactory } = await import("@/services/music-provider");

      // 确保提供商已初始化
      await MusicProviderFactory.initialize();

      // Get the music provider with fallback mechanism
      let musicProvider = MusicProviderFactory.getProvider(provider);
      let actualProvider = provider;

      // 如果指定的提供商不可用，尝试使用默认提供商
      if (!musicProvider) {
        console.log(`Provider "${provider}" not available, trying default provider`);
        musicProvider = MusicProviderFactory.getDefaultProvider();
        if (musicProvider) {
          actualProvider = musicProvider.getProviderInfo().name;
          console.log(`Using fallback provider: ${actualProvider}`);
        }
      }

      if (!musicProvider) {
        return respErr(`No music provider available. Please check your API configuration.`);
      }

      // Optimize prompt for better looping
      const { LoopPromptOptimizer } = await import("@/services/loop-prompt-optimizer");
      const optimizedPrompt = LoopPromptOptimizer.optimizePrompt(prompt, {
        duration,
        style,
        mood,
        bpm,
        provider: actualProvider as "volcano" | "mubert" | "suno",
      });
      
      // Generate negative prompt to avoid loop-breaking elements
      const negativePrompt = LoopPromptOptimizer.generateNegativePrompt({
        duration,
        style,
        mood,
        bpm,
        provider: actualProvider as "volcano" | "mubert" | "suno",
      });

      console.log("Optimized prompt for looping:", {
        original: prompt,
        optimized: optimizedPrompt,
        negative: negativePrompt,
      });

      // Start the generation process with optimized prompt
      const generationResult = await musicProvider.generateMusic(optimizedPrompt, duration, {
        provider: actualProvider,
        style,
        mood,
        bpm,
        // New fields for API compatibility
        genre: genres,
        instrument: instruments,
        theme: themes,
        quality: options?.quality || "standard",
        seed: options?.seed,
        temperature: options?.temperature || 0.7, // Slightly lower temperature for more consistent loops
        guidance_scale: options?.guidance_scale || 7.5, // Higher guidance for better prompt following
        negative_prompt: negativePrompt,
      });

      // Update the generation record with provider task ID
      await MusicGenerationService.updateGenerationProviderTaskId(
        generation_uuid,
        generationResult.task_id
      );

      console.log("Music generation started:", {
        generation_uuid,
        provider_task_id: generationResult.task_id,
        user_uuid,
        prompt,
        style,
        mood,
        bpm,
        duration,
        // New fields
        genres,
        instruments,
        themes,
        provider: actualProvider,
      });
    } catch (error) {
      console.error("Failed to start music generation:", error);
      // Update generation status to failed
      const { MusicGenerationService } = await import("@/services/music-generation");
      const errorMessage = error instanceof Error ? error.message : String(error);
      await MusicGenerationService.updateGenerationStatus(generation_uuid, "failed", errorMessage);
      return respErr("Failed to start music generation");
    }

    // Estimate completion time based on duration and current load
    const estimated_completion_time = duration * 2; // Rough estimate: 2x the track duration

    const response: GenerateMusicResponse = {
      code: 0,
      message: "Music generation started",
      data: {
        generation_uuid,
        estimated_completion_time,
        status: "pending",
      },
    };

    return respData(response.data);
  } catch (error) {
    console.error("Music generation error:", error);
    return respErr("Failed to start music generation");
  }
}

// GET method to retrieve generation info
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const generation_uuid = url.searchParams.get("generation_uuid");

    if (!generation_uuid) {
      return respErr("Missing generation_uuid parameter");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // TODO: Implement get generation status
    // This would fetch the generation record and return current status

    return respData({
      generation_uuid,
      status: "pending",
      message: "Generation status endpoint not fully implemented",
    });
  } catch (error) {
    console.error("Get generation error:", error);
    return respErr("Failed to get generation status");
  }
}
