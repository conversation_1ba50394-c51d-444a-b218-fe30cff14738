import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findMusicGenerationByUuid, updateMusicGenerationStatus } from "@/models/music-generation";
import { findTrackByGenerationUuid, insertTrack } from "@/models/track";
import { MusicStatusRequest, MusicStatusResponse } from "@/types/music-api";
import { MusicProviderFactory } from "@/services/music-provider";
import { audioFileProcessor } from "@/services/audio-file-processor";
import { generateTrackSlug } from "@/lib/track-slug";
import { randomUUID } from "crypto";

/**
 * 主动查询提供商状态并更新数据库
 */
async function updateGenerationStatus(generation: any) {
  // 如果已经完成或失败，不需要再查询
  if (generation.status === "completed" || generation.status === "failed") {
    return generation;
  }

  try {
    // 获取提供商实例
    await MusicProviderFactory.initialize();
    const provider = MusicProviderFactory.getProvider(generation.provider || "volcano");

    if (!provider) {
      console.error("Provider not found:", generation.provider);
      return generation;
    }

    // 检查是否有 provider_task_id
    if (!generation.provider_task_id) {
      console.log(`No provider_task_id for generation ${generation.uuid}, skipping provider check`);
      return generation;
    }

    // 查询提供商状态
    const providerStatus = await provider.checkStatus(generation.provider_task_id);
    console.log("Provider status for task", generation.task_id, ":", providerStatus);

    // 更新数据库状态
    const updatedGeneration = await updateMusicGenerationStatus(
      generation.uuid,
      providerStatus.status,
      providerStatus.error
    );

    // 如果完成了，创建音轨记录
    if (providerStatus.status === "completed" && providerStatus.result) {
      const existingTrack = await findTrackByGenerationUuid(generation.uuid);
      if (!existingTrack) {
        const trackUuid = randomUUID();

        // 生成slug
        const slug = generateTrackSlug({
          prompt: generation.prompt,
          bpm: generation.bpm,
          uuid: trackUuid,
          style: generation.style
        });

        // 创建音轨记录（先使用原始 URL）
        const track = await insertTrack({
          uuid: trackUuid,
          user_uuid: generation.user_uuid,
          generation_uuid: generation.uuid,
          title: generation.prompt.substring(0, 100),
          slug: slug,
          prompt: generation.prompt, // 添加必需的 prompt 字段
          style: generation.style,
          mood: generation.mood,
          bpm: generation.bpm,
          // New fields for API compatibility
          genre: generation.genre,
          instrument: generation.instrument,
          theme: generation.theme,
          file_url: providerStatus.result.file_url,
          file_size: providerStatus.result.file_size || 0,
          duration: generation.duration,
          metadata: JSON.stringify(providerStatus.result.metadata || {}),
          is_public: true, // 默认公开
          created_at: new Date(),
          updated_at: new Date(),
        });

        // 异步处理文件下载和存储（不阻塞响应）
        if (track) {
          audioFileProcessor.processAudioFile(
            trackUuid,
            providerStatus.result.file_url,
            providerStatus.result.file_size
          ).then(result => {
            if (result.success) {
              console.log(`Audio file processed successfully for track ${trackUuid}`);
            } else {
              console.error(`Audio file processing failed for track ${trackUuid}:`, result.error);
            }
          }).catch(error => {
            console.error(`Audio file processing error for track ${trackUuid}:`, error);
          });
        }
      }
    }

    return updatedGeneration || generation;
  } catch (error) {
    console.error("Failed to update generation status:", error);
    return generation;
  }
}

export async function POST(req: Request) {
  try {
    const body: MusicStatusRequest = await req.json();
    const { generation_uuid } = body;

    if (!generation_uuid) {
      return respErr("Missing generation_uuid parameter");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the generation record
    let generation = await findMusicGenerationByUuid(generation_uuid);
    if (!generation) {
      return respErr("Generation not found");
    }

    // Check if user owns this generation
    if (generation.user_uuid !== user_uuid) {
      return respErr("Access denied");
    }

    // 主动查询并更新状态
    generation = await updateGenerationStatus(generation);

    // Check if generation still exists after update
    if (!generation) {
      return respErr("Generation not found after status update");
    }

    // Calculate progress based on status and time elapsed
    let progress = 0;
    switch (generation.status) {
      case "pending":
        progress = 0;
        break;
      case "processing":
        // Estimate progress based on time elapsed
        const elapsed = Date.now() - new Date(generation.created_at!).getTime();
        const estimatedTotal = generation.duration * 2 * 1000; // 2x duration in ms
        progress = Math.min(90, Math.floor((elapsed / estimatedTotal) * 100));
        break;
      case "completed":
        progress = 100;
        break;
      case "failed":
        progress = 0;
        break;
    }

    // If completed, try to get the track
    let track = undefined;
    if (generation.status === "completed") {
      track = await findTrackByGenerationUuid(generation_uuid);
    }

    const response: MusicStatusResponse = {
      code: 0,
      message: "ok",
      data: {
        generation_uuid,
        status: generation.status,
        progress,
        track: track || undefined,
        error_message: generation.error_message || undefined,
      },
    };

    return respData(response.data);
  } catch (error) {
    console.error("Music status error:", error);
    return respErr("Failed to get music generation status");
  }
}

// GET method for status check
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const generation_uuid = url.searchParams.get("generation_uuid");

    if (!generation_uuid) {
      return respErr("Missing generation_uuid parameter");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the generation record
    let generation = await findMusicGenerationByUuid(generation_uuid);
    if (!generation) {
      return respErr("Generation not found");
    }

    // Check if user owns this generation
    if (generation.user_uuid !== user_uuid) {
      return respErr("Access denied");
    }

    // 主动查询并更新状态
    generation = await updateGenerationStatus(generation);

    // Check if generation still exists after update
    if (!generation) {
      return respErr("Generation not found after status update");
    }

    // Calculate progress
    let progress = 0;
    switch (generation.status) {
      case "pending":
        progress = 0;
        break;
      case "processing":
        const elapsed = Date.now() - new Date(generation.created_at!).getTime();
        const estimatedTotal = generation.duration * 2 * 1000;
        progress = Math.min(90, Math.floor((elapsed / estimatedTotal) * 100));
        break;
      case "completed":
        progress = 100;
        break;
      case "failed":
        progress = 0;
        break;
    }

    // If completed, get the track
    let track = undefined;
    if (generation.status === "completed") {
      track = await findTrackByGenerationUuid(generation_uuid);
    }

    return respData({
      generation_uuid,
      status: generation.status,
      progress,
      track: track || undefined,
      error_message: generation.error_message || undefined,
    });
  } catch (error) {
    console.error("Music status error:", error);
    return respErr("Failed to get music generation status");
  }
}
