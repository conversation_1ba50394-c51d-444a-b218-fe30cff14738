import {
  insertMusicGeneration,
  findMusicGenerationByUuid,
  updateMusicGenerationStatus,
  updateMusicGenerationProviderTaskId,
  getMusicGenerationsByStatus,
  getStaleGenerations,
  countUserGenerationsInPeriod
} from "@/models/music-generation";
import { insertTrack } from "@/models/track";
import { getUuid } from "@/lib/hash";
import { newStorage } from "@/lib/storage";
import { MusicGeneration, MusicGenerationRequest, Track } from "@/types/music";
import { WatermarkService } from "./watermark-service";
import { findUserByUuid } from "@/models/user";

// Music generation service class
export class MusicGenerationService {
  
  // Create a new music generation request
  static async createGeneration(
    user_uuid: string,
    request: MusicGenerationRequest
  ): Promise<MusicGeneration | null> {
    try {
      const generation_uuid = getUuid();
      
      const generationData = {
        uuid: generation_uuid,
        user_uuid,
        prompt: request.prompt,
        style: request.style || null,
        mood: request.mood || null,
        bpm: request.bpm || null,
        duration: request.duration,
        provider: request.provider || "mubert",
        status: "pending" as const,
        created_at: new Date(),
      };

      const generation = await insertMusicGeneration(generationData);
      return generation as MusicGeneration;
    } catch (error) {
      console.error("Failed to create music generation:", error);
      return null;
    }
  }

  // Get generation status
  static async getGenerationStatus(generation_uuid: string): Promise<MusicGeneration | null> {
    try {
      const generation = await findMusicGenerationByUuid(generation_uuid);
      return generation as MusicGeneration;
    } catch (error) {
      console.error("Failed to get generation status:", error);
      return null;
    }
  }

  // Update generation status
  static async updateGenerationStatus(
    generation_uuid: string,
    status: "pending" | "processing" | "completed" | "failed",
    error_message?: string
  ): Promise<boolean> {
    try {
      const result = await updateMusicGenerationStatus(generation_uuid, status, error_message);
      return !!result;
    } catch (error) {
      console.error("Failed to update generation status:", error);
      return false;
    }
  }

  // Update provider task ID
  static async updateGenerationProviderTaskId(
    generation_uuid: string,
    provider_task_id: string
  ): Promise<boolean> {
    try {
      const result = await updateMusicGenerationProviderTaskId(generation_uuid, provider_task_id);
      return !!result;
    } catch (error) {
      console.error("Failed to update provider task ID:", error);
      return false;
    }
  }

  // Process completed generation and create track
  static async processCompletedGeneration(
    generation_uuid: string,
    file_url: string,
    file_size: number,
    metadata?: any
  ): Promise<Track | null> {
    try {
      const generation = await findMusicGenerationByUuid(generation_uuid);
      if (!generation) {
        throw new Error("Generation not found");
      }

      // Generate track UUID and slug
      const track_uuid = getUuid();
      const slug = this.generateTrackSlug(generation.prompt, generation.bpm || undefined);

      // Create track record
      const trackData = {
        uuid: track_uuid,
        generation_uuid,
        user_uuid: generation.user_uuid,
        title: this.generateTrackTitle(generation.prompt, generation.style || undefined),
        slug,
        prompt: generation.prompt,
        style: generation.style,
        mood: generation.mood,
        bpm: generation.bpm,
        duration: generation.duration,
        // New fields for API compatibility
        genre: generation.genre,
        instrument: generation.instrument,
        theme: generation.theme,
        file_url,
        file_size,
        file_format: "mp3" as const,
        metadata: metadata || null,
        download_count: 0,
        is_public: false,
        created_at: new Date(),
      };

      const track = await insertTrack(trackData);
      
      if (track) {
        // Update generation status to completed
        await this.updateGenerationStatus(generation_uuid, "completed");
      }

      return track as Track;
    } catch (error) {
      console.error("Failed to process completed generation:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.updateGenerationStatus(generation_uuid, "failed", errorMessage);
      return null;
    }
  }

  // Generate track title from prompt and style
  private static generateTrackTitle(prompt: string, style?: string): string {
    const words = prompt.split(' ').slice(0, 5); // Take first 5 words
    let title = words.map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');

    if (style) {
      title += ` (${style})`;
    }

    return title;
  }

  // Generate SEO-friendly slug
  private static generateTrackSlug(prompt: string, bpm?: number): string {
    const words = prompt.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .split(' ')
      .filter(word => word.length > 0)
      .slice(0, 6); // Take first 6 words

    let slug = words.join('-');
    
    if (bpm) {
      slug += `-${bpm}bpm`;
    }

    // Add random suffix to ensure uniqueness
    const suffix = Math.random().toString(36).substring(2, 8);
    slug += `-${suffix}`;

    return slug;
  }

  // Check user generation limits
  static async checkUserGenerationLimits(
    user_uuid: string,
    is_pro: boolean
  ): Promise<{ allowed: boolean; remaining: number; limit: number }> {
    try {
      const limit = is_pro ? 20 : 3; // Pro: 20/month, Free: 3/month
      const hoursInMonth = 24 * 30; // Approximate month
      
      const used = await countUserGenerationsInPeriod(user_uuid, hoursInMonth);
      const remaining = Math.max(0, limit - used);
      
      return {
        allowed: remaining > 0,
        remaining,
        limit,
      };
    } catch (error) {
      console.error("Failed to check generation limits:", error);
      return { allowed: false, remaining: 0, limit: 0 };
    }
  }

  // Get pending generations for processing
  static async getPendingGenerations(limit: number = 10): Promise<MusicGeneration[]> {
    try {
      const generations = await getMusicGenerationsByStatus("pending", limit);
      return generations as MusicGeneration[];
    } catch (error) {
      console.error("Failed to get pending generations:", error);
      return [];
    }
  }

  // Get processing generations
  static async getProcessingGenerations(limit: number = 50): Promise<MusicGeneration[]> {
    try {
      const generations = await getMusicGenerationsByStatus("processing", limit);
      return generations as MusicGeneration[];
    } catch (error) {
      console.error("Failed to get processing generations:", error);
      return [];
    }
  }

  // Get stale generations that need cleanup
  static async getStaleGenerations(minutes: number = 30): Promise<MusicGeneration[]> {
    try {
      const generations = await getStaleGenerations(minutes);
      return generations as MusicGeneration[];
    } catch (error) {
      console.error("Failed to get stale generations:", error);
      return [];
    }
  }

  // Cleanup stale generations
  static async cleanupStaleGenerations(minutes: number = 30): Promise<number> {
    try {
      const staleGenerations = await this.getStaleGenerations(minutes);
      let cleanedCount = 0;

      for (const generation of staleGenerations) {
        const success = await this.updateGenerationStatus(
          generation.uuid,
          "failed",
          "Generation timed out"
        );
        if (success) {
          cleanedCount++;
        }
      }

      console.log(`Cleaned up ${cleanedCount} stale generations`);
      return cleanedCount;
    } catch (error) {
      console.error("Failed to cleanup stale generations:", error);
      return 0;
    }
  }

  // Validate generation request
  static validateGenerationRequest(request: MusicGenerationRequest): { valid: boolean; error?: string } {
    if (!request.prompt || request.prompt.trim().length === 0) {
      return { valid: false, error: "Prompt is required" };
    }

    if (request.prompt.length > 500) {
      return { valid: false, error: "Prompt must be less than 500 characters" };
    }

    if (![15, 30, 60].includes(request.duration)) {
      return { valid: false, error: "Duration must be 15, 30, or 60 seconds" };
    }

    if (request.bpm && (request.bpm < 60 || request.bpm > 200)) {
      return { valid: false, error: "BPM must be between 60 and 200" };
    }

    if (request.style && request.style.length > 100) {
      return { valid: false, error: "Style must be less than 100 characters" };
    }

    if (request.mood && request.mood.length > 100) {
      return { valid: false, error: "Mood must be less than 100 characters" };
    }

    return { valid: true };
  }

  // Complete generation with watermark processing
  static async completeGeneration(
    generation_uuid: string,
    originalAudioUrl: string,
    trackData: Partial<Track>
  ): Promise<{ success: boolean; track?: Track; error?: string }> {
    try {
      const generation = await findMusicGenerationByUuid(generation_uuid);
      if (!generation) {
        return { success: false, error: "Generation not found" };
      }

      // Get user information to determine subscription status
      const user = await findUserByUuid(generation.user_uuid);
      if (!user) {
        return { success: false, error: "User not found" };
      }

      // Determine user subscription status (mock implementation)
      const userSubscription = {
        plan: (user.subscription_plan as "free" | "professional" | "commercial") || "free",
        is_active: user.subscription_status === "active",
        expires_at: user.subscription_expires_at?.toISOString(),
      };

      // Process audio with watermark if needed
      const processedAudio = await WatermarkService.processAudioForUser(
        originalAudioUrl,
        userSubscription,
        generation.duration,
        generation.uuid
      );

      // Create track record
      const trackUuid = getUuid();
      const track = await insertTrack({
        uuid: trackUuid,
        generation_uuid: generation_uuid,
        user_uuid: generation.user_uuid,
        title: trackData.title || `Generated Track ${new Date().toLocaleDateString()}`,
        prompt: generation.prompt,
        style: generation.style,
        mood: generation.mood,
        bpm: generation.bpm,
        duration: generation.duration,
        // New fields for API compatibility
        genre: generation.genre,
        instrument: generation.instrument,
        theme: generation.theme,
        file_url: processedAudio.processed_url,
        file_size: trackData.file_size || 0,
        file_format: trackData.file_format || "mp3",
        is_public: trackData.is_public || false,
        is_premium: !processedAudio.has_watermark,
        has_watermark: processedAudio.has_watermark,
        original_file_url: processedAudio.has_watermark ? originalAudioUrl : null,
        created_at: new Date(),
      });

      if (!track) {
        return { success: false, error: "Failed to create track record" };
      }

      // Update generation status
      await this.updateGenerationStatus(generation_uuid, "completed");

      console.log("Generation completed with watermark processing:", {
        generation_uuid,
        track_uuid: trackUuid,
        has_watermark: processedAudio.has_watermark,
        processing_time: processedAudio.processing_time,
      });

      return { success: true, track: track as Track };
    } catch (error) {
      console.error("Failed to complete generation:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.updateGenerationStatus(generation_uuid, "failed", errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  // Get watermark info for user
  static async getUserWatermarkInfo(user_uuid: string): Promise<{
    has_watermark: boolean;
    watermark_type: string;
    removal_available: boolean;
    upgrade_required: boolean;
    upgrade_message: string;
  }> {
    try {
      const user = await findUserByUuid(user_uuid);
      if (!user) {
        throw new Error("User not found");
      }

      const userSubscription = {
        plan: (user.subscription_plan as "free" | "professional" | "commercial") || "free",
        is_active: user.subscription_status === "active",
        expires_at: user.subscription_expires_at?.toISOString(),
      };

      const watermarkInfo = WatermarkService.getWatermarkInfo(userSubscription);
      const upgradeMessage = WatermarkService.getUpgradeMessage(userSubscription);

      return {
        ...watermarkInfo,
        upgrade_message: upgradeMessage,
      };
    } catch (error) {
      console.error("Failed to get watermark info:", error);
      return {
        has_watermark: true,
        watermark_type: "audio",
        removal_available: true,
        upgrade_required: true,
        upgrade_message: "Upgrade to remove watermarks",
      };
    }
  }

  // Estimate generation time
  static estimateGenerationTime(duration: number, provider: string = "mubert"): number {
    // Base estimation: 2x the track duration
    let baseTime = duration * 2;

    // Provider-specific adjustments
    switch (provider) {
      case "suno":
        baseTime *= 1.5; // Suno is slower but higher quality
        break;
      case "mubert":
        baseTime *= 1.0; // Mubert is baseline
        break;
      default:
        baseTime *= 1.2; // Unknown providers get penalty
    }

    // Add some randomness for realistic estimation
    const variance = baseTime * 0.3;
    const randomFactor = (Math.random() - 0.5) * variance;

    return Math.max(10, Math.round(baseTime + randomFactor)); // Minimum 10 seconds
  }
}
