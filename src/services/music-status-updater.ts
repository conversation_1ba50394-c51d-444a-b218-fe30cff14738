/**
 * 音乐生成状态更新服务
 * 定期检查进行中的音乐生成任务并更新状态
 */

import { findPendingMusicGenerations, updateMusicGenerationStatus } from "@/models/music-generation";
import { findTrackByGenerationUuid } from "@/models/track";
import { MusicProviderFactory } from "@/services/music-provider";
import { audioFileProcessor } from "./audio-file-processor";
import { TrackCreationService } from "@/services/track-creation-service";
import { randomUUID } from "crypto";

export class MusicStatusUpdater {
  private static instance: MusicStatusUpdater;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private readonly checkInterval = 10000; // 10秒检查一次

  private constructor() {}

  static getInstance(): MusicStatusUpdater {
    if (!MusicStatusUpdater.instance) {
      MusicStatusUpdater.instance = new MusicStatusUpdater();
    }
    return MusicStatusUpdater.instance;
  }

  /**
   * 启动状态更新服务
   */
  start() {
    if (this.isRunning) {
      console.log("Music status updater is already running");
      return;
    }

    console.log("Starting music status updater...");
    this.isRunning = true;

    // 立即执行一次
    this.checkAndUpdateStatuses();

    // 设置定期检查
    this.intervalId = setInterval(() => {
      this.checkAndUpdateStatuses();
    }, this.checkInterval);
  }

  /**
   * 停止状态更新服务
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log("Stopping music status updater...");
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * 检查并更新所有进行中的音乐生成状态
   */
  private async checkAndUpdateStatuses() {
    try {
      // 获取所有进行中的生成任务
      const pendingGenerations = await findPendingMusicGenerations();
      
      if (pendingGenerations.length === 0) {
        console.log("No pending music generations to check");
        return;
      }

      console.log(`Checking ${pendingGenerations.length} pending music generations...`);

      // 确保提供商已初始化
      await MusicProviderFactory.initialize();

      // 并行处理所有任务
      const updatePromises = pendingGenerations.map(generation => 
        this.updateSingleGeneration(generation)
      );

      await Promise.allSettled(updatePromises);
      
    } catch (error) {
      console.error("Error in music status updater:", error);
    }
  }

  /**
   * 更新单个音乐生成任务的状态
   */
  private async updateSingleGeneration(generation: any) {
    try {
      console.log(`Checking status for generation ${generation.uuid} (task: ${generation.provider_task_id})`);

      // 检查是否有 provider_task_id
      if (!generation.provider_task_id) {
        console.log(`No provider_task_id for generation ${generation.uuid}, skipping`);
        return;
      }

      // 获取提供商实例
      const provider = MusicProviderFactory.getProvider(generation.provider || "volcano");
      if (!provider) {
        console.error(`Provider not found: ${generation.provider}`);
        return;
      }

      // 查询提供商状态
      const providerStatus = await provider.checkStatus(generation.provider_task_id);
      console.log(`Provider status for ${generation.uuid}:`, providerStatus);

      // 如果状态没有变化，跳过更新
      if (providerStatus.status === generation.status) {
        console.log(`Status unchanged for ${generation.uuid}: ${providerStatus.status}`);
        return;
      }

      // 更新数据库状态
      const updatedGeneration = await updateMusicGenerationStatus(
        generation.uuid,
        providerStatus.status,
        providerStatus.error
      );

      console.log(`Updated generation ${generation.uuid} status: ${generation.status} -> ${providerStatus.status}`);

      // 如果完成了，创建音轨记录
      if (providerStatus.status === "completed" && providerStatus.result) {
        await this.createTrackIfNotExists(generation, providerStatus.result);
      }

    } catch (error) {
      console.error(`Failed to update generation ${generation.uuid}:`, error);
      
      // 如果查询失败多次，可以考虑标记为失败
      // 这里暂时只记录错误，不改变状态
    }
  }

  /**
   * 创建音轨记录（如果不存在）
   */
  private async createTrackIfNotExists(generation: any, result: any) {
    try {
      // 检查是否已经存在音轨
      const existingTrack = await findTrackByGenerationUuid(generation.uuid);
      if (existingTrack) {
        console.log(`Track already exists for generation ${generation.uuid}`);
        return;
      }

      // 使用统一的track创建服务，正确处理premium和水印
      const track = await TrackCreationService.createTrack({
        generation,
        file_url: result.file_url,
        file_size: result.file_size || 0,
        metadata: result.metadata || {},
        is_public: true, // 默认公开
      });

      console.log(`Created track for generation ${generation.uuid}:`, track?.uuid);

      // 异步处理文件下载和存储（不阻塞状态更新）
      if (track) {
        audioFileProcessor.processAudioFile(
          track.uuid,
          result.file_url,
          result.file_size
        ).then(processResult => {
          if (processResult.success) {
            console.log(`Audio file processed successfully for track ${trackUuid}`);
          } else {
            console.error(`Audio file processing failed for track ${trackUuid}:`, processResult.error);
          }
        }).catch(error => {
          console.error(`Audio file processing error for track ${trackUuid}:`, error);
        });
      }

    } catch (error) {
      console.error(`Failed to create track for generation ${generation.uuid}:`, error);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      checkInterval: this.checkInterval,
    };
  }
}

// 导出单例实例
export const musicStatusUpdater = MusicStatusUpdater.getInstance();

// 在模块加载时自动启动（仅在服务器端）
if (typeof window === "undefined") {
  // 延迟启动，确保数据库连接已建立
  setTimeout(() => {
    musicStatusUpdater.start();
  }, 5000);
}
