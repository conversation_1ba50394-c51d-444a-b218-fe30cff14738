/**
 * 文件存储服务
 * 处理音频文件的下载、存储和管理
 * 支持S3云存储和本地存储
 */

import fs from 'fs';
import path from 'path';
import { randomUUID } from 'crypto';
import { Storage, newStorage } from '@/lib/storage';

export interface FileDownloadResult {
  success: boolean;
  localPath?: string;
  publicUrl?: string;
  fileSize?: number;
  error?: string;
  s3Key?: string;
  downloadSuccess?: boolean;
}

export class FileStorageService {
  private static instance: FileStorageService;
  private readonly uploadDir: string;
  private readonly publicBaseUrl: string;
  private readonly storage: Storage;
  private readonly useS3: boolean;
  private readonly s3Bucket: string;

  private constructor() {
    // 配置上传目录（本地存储备用）
    this.uploadDir = path.join(process.cwd(), 'public', 'uploads', 'music');
    this.publicBaseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

    // 初始化S3存储
    this.storage = newStorage();
    this.s3Bucket = process.env.STORAGE_BUCKET || '';
    this.useS3 = !!(process.env.STORAGE_ENDPOINT && process.env.STORAGE_ACCESS_KEY && this.s3Bucket);

    // 确保本地上传目录存在（作为备用）
    this.ensureUploadDir();

    console.log(`FileStorageService initialized: ${this.useS3 ? 'S3' : 'Local'} storage`);
  }

  static getInstance(): FileStorageService {
    if (!FileStorageService.instance) {
      FileStorageService.instance = new FileStorageService();
    }
    return FileStorageService.instance;
  }

  /**
   * 确保上传目录存在
   */
  private ensureUploadDir(): void {
    try {
      if (!fs.existsSync(this.uploadDir)) {
        fs.mkdirSync(this.uploadDir, { recursive: true });
        console.log(`Created upload directory: ${this.uploadDir}`);
      }
    } catch (error) {
      console.error('Failed to create upload directory:', error);
    }
  }

  /**
   * 从 URL 下载文件并保存到S3或本地
   */
  async downloadAndSaveFile(
    sourceUrl: string,
    originalFilename?: string,
    fileExtension: string = 'wav'
  ): Promise<FileDownloadResult> {
    try {
      console.log(`Starting download from: ${sourceUrl}`);

      // 生成唯一的文件名和S3 key
      const fileId = randomUUID();
      const filename = `${fileId}.${fileExtension}`;
      const s3Key = `music/${filename}`;

      // 下载文件
      const response = await fetch(sourceUrl);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 获取文件大小
      const contentLength = response.headers.get('content-length');
      const fileSize = contentLength ? parseInt(contentLength, 10) : 0;

      // 获取文件内容
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      if (this.useS3) {
        // 上传到S3
        try {
          const uploadResult = await this.storage.uploadFile({
            body: buffer,
            key: s3Key,
            contentType: `audio/${fileExtension}`,
            bucket: this.s3Bucket,
            disposition: 'inline'
          });

          console.log(`File uploaded to S3 successfully: ${uploadResult.url} (${fileSize} bytes)`);

          return {
            success: true,
            publicUrl: uploadResult.url,
            fileSize,
            s3Key,
            downloadSuccess: true,
          };
        } catch (s3Error) {
          console.error('S3 upload failed, falling back to local storage:', s3Error);
          // 继续执行本地存储逻辑
        }
      }

      // 本地存储（作为备用或主要方式）
      const localPath = path.join(this.uploadDir, filename);
      const publicUrl = `${this.publicBaseUrl}/uploads/music/${filename}`;

      fs.writeFileSync(localPath, buffer);

      console.log(`File downloaded successfully: ${localPath} (${fileSize} bytes)`);

      return {
        success: true,
        localPath,
        publicUrl,
        fileSize,
        downloadSuccess: true,
      };

    } catch (error) {
      console.error('File download failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        downloadSuccess: false,
      };
    }
  }

  /**
   * 删除文件（S3或本地）
   */
  async deleteFile(publicUrl: string, s3Key?: string): Promise<boolean> {
    try {
      let deleted = false;

      // 如果有S3 key，尝试从S3删除
      if (this.useS3 && s3Key) {
        try {
          // 注意：当前的Storage类没有delete方法，这里先记录日志
          console.log(`Would delete S3 file: ${s3Key}`);
          // TODO: 实现S3删除功能
          deleted = true;
        } catch (s3Error) {
          console.error('S3 deletion failed:', s3Error);
        }
      }

      // 尝试删除本地文件（如果存在）
      const filename = path.basename(publicUrl);
      const localPath = path.join(this.uploadDir, filename);

      if (fs.existsSync(localPath)) {
        fs.unlinkSync(localPath);
        console.log(`Local file deleted: ${localPath}`);
        deleted = true;
      }

      return deleted;
    } catch (error) {
      console.error('File deletion failed:', error);
      return false;
    }
  }

  /**
   * 检查文件是否存在
   */
  fileExists(publicUrl: string): boolean {
    try {
      const filename = path.basename(publicUrl);
      const localPath = path.join(this.uploadDir, filename);
      return fs.existsSync(localPath);
    } catch (error) {
      console.error('File existence check failed:', error);
      return false;
    }
  }

  /**
   * 获取文件信息
   */
  getFileInfo(publicUrl: string): { size: number; exists: boolean } | null {
    try {
      const filename = path.basename(publicUrl);
      const localPath = path.join(this.uploadDir, filename);
      
      if (fs.existsSync(localPath)) {
        const stats = fs.statSync(localPath);
        return {
          size: stats.size,
          exists: true,
        };
      } else {
        return {
          size: 0,
          exists: false,
        };
      }
    } catch (error) {
      console.error('Get file info failed:', error);
      return null;
    }
  }

  /**
   * 清理过期文件（可选功能）
   */
  async cleanupOldFiles(maxAgeHours: number = 24 * 7): Promise<number> {
    try {
      const files = fs.readdirSync(this.uploadDir);
      const maxAge = maxAgeHours * 60 * 60 * 1000; // 转换为毫秒
      const now = Date.now();
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(this.uploadDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`Cleaned up old file: ${file}`);
        }
      }

      console.log(`Cleanup completed: ${deletedCount} files deleted`);
      return deletedCount;

    } catch (error) {
      console.error('Cleanup failed:', error);
      return 0;
    }
  }
}

// 导出单例实例
export const fileStorageService = FileStorageService.getInstance();
