import {
  pgTable,
  serial,
  varchar,
  text,
  boolean,
  integer,
  timestamp,
  unique,
  uniqueIndex,
  decimal,
  json,
} from "drizzle-orm/pg-core";

// Users table
export const users = pgTable(
  "users",
  {
    id: integer().primaryKey().generatedAlwaysAsIdentity(),
    uuid: varchar({ length: 255 }).notNull().unique(),
    email: varchar({ length: 255 }).notNull(),
    created_at: timestamp({ withTimezone: true }),
    nickname: varchar({ length: 255 }),
    avatar_url: varchar({ length: 255 }),
    locale: varchar({ length: 50 }),
    signin_type: varchar({ length: 50 }),
    signin_ip: varchar({ length: 255 }),
    signin_provider: varchar({ length: 50 }),
    signin_openid: varchar({ length: 255 }),
    invite_code: varchar({ length: 255 }).notNull().default(""),
    updated_at: timestamp({ withTimezone: true }),
    invited_by: varchar({ length: 255 }).notNull().default(""),
    is_affiliate: boolean().notNull().default(false),
    // Subscription fields
    subscription_plan: varchar({ length: 50 }).default("free"),
    subscription_status: varchar({ length: 50 }).default("inactive"),
    subscription_expires_at: timestamp({ withTimezone: true }),
    subscription_stripe_id: varchar({ length: 255 }),
    // Credits system
    credits: integer().notNull().default(0),
  },
  (table) => [
    uniqueIndex("email_provider_unique_idx").on(
      table.email,
      table.signin_provider
    ),
  ]
);

// Orders table
export const orders = pgTable("orders", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  order_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp({ withTimezone: true }),
  user_uuid: varchar({ length: 255 }).notNull().default(""),
  user_email: varchar({ length: 255 }).notNull().default(""),
  amount: integer().notNull(),
  interval: varchar({ length: 50 }),
  expired_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }).notNull(),
  stripe_session_id: varchar({ length: 255 }),
  credits: integer().notNull(),
  currency: varchar({ length: 50 }),
  sub_id: varchar({ length: 255 }),
  sub_interval_count: integer(),
  sub_cycle_anchor: integer(),
  sub_period_end: integer(),
  sub_period_start: integer(),
  sub_times: integer(),
  product_id: varchar({ length: 255 }),
  product_name: varchar({ length: 255 }),
  valid_months: integer(),
  order_detail: text(),
  paid_at: timestamp({ withTimezone: true }),
  paid_email: varchar({ length: 255 }),
  paid_detail: text(),
});

// API Keys table
export const apikeys = pgTable("apikeys", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  api_key: varchar({ length: 255 }).notNull().unique(),
  title: varchar({ length: 100 }),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
});

// Credits table
export const credits = pgTable("credits", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  trans_no: varchar({ length: 255 }).notNull().unique(),
  created_at: timestamp({ withTimezone: true }),
  user_uuid: varchar({ length: 255 }).notNull(),
  trans_type: varchar({ length: 50 }).notNull(),
  credits: integer().notNull(),
  order_no: varchar({ length: 255 }),
  expired_at: timestamp({ withTimezone: true }),
});

// Posts table
export const posts = pgTable("posts", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  slug: varchar({ length: 255 }),
  title: varchar({ length: 255 }),
  description: text(),
  content: text(),
  created_at: timestamp({ withTimezone: true }),
  updated_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
  cover_url: varchar({ length: 255 }),
  author_name: varchar({ length: 255 }),
  author_avatar_url: varchar({ length: 255 }),
  locale: varchar({ length: 50 }),
});

// Affiliates table
export const affiliates = pgTable("affiliates", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }).notNull().default(""),
  invited_by: varchar({ length: 255 }).notNull(),
  paid_order_no: varchar({ length: 255 }).notNull().default(""),
  paid_amount: integer().notNull().default(0),
  reward_percent: integer().notNull().default(0),
  reward_amount: integer().notNull().default(0),
});

// Feedbacks table
export const feedbacks = pgTable("feedbacks", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  created_at: timestamp({ withTimezone: true }),
  status: varchar({ length: 50 }),
  user_uuid: varchar({ length: 255 }),
  content: text(),
  rating: integer(),
});

// Music Generations table - 音乐生成请求记录
export const musicGenerations = pgTable("music_generations", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  user_uuid: varchar({ length: 255 }).notNull(),
  prompt: text().notNull(),
  style: varchar({ length: 100 }),
  mood: varchar({ length: 100 }),
  bpm: integer(),
  duration: integer().notNull(), // 15, 30, or 60 seconds
  // New fields for API compatibility
  genre: json(), // Array of genre strings
  instrument: json(), // Array of instrument strings
  theme: json(), // Array of theme strings
  provider: varchar({ length: 50 }).notNull(), // mubert, suno, etc.
  provider_task_id: varchar({ length: 255 }),
  status: varchar({ length: 50 }).notNull().default("pending"), // pending, processing, completed, failed
  error_message: text(),
  created_at: timestamp({ withTimezone: true }).defaultNow(),
  updated_at: timestamp({ withTimezone: true }),
  completed_at: timestamp({ withTimezone: true }),
});

// Tracks table - 生成的音乐轨道
export const tracks = pgTable("tracks", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  generation_uuid: varchar({ length: 255 }).notNull(), // 关联到 music_generations
  user_uuid: varchar({ length: 255 }).notNull(),
  title: varchar({ length: 255 }),
  slug: varchar({ length: 255 }).unique(), // SEO friendly URL slug
  prompt: text().notNull(),
  style: varchar({ length: 100 }),
  mood: varchar({ length: 100 }),
  bpm: integer(),
  duration: integer().notNull(),
  // New fields for API compatibility
  genre: json(), // Array of genre strings
  instrument: json(), // Array of instrument strings
  theme: json(), // Array of theme strings
  key_signature: varchar({ length: 10 }), // C, D, E, etc.
  file_url: varchar({ length: 500 }).notNull(),
  file_size: integer(),
  file_format: varchar({ length: 10 }).notNull().default("mp3"), // mp3, wav
  waveform_data: json(), // 波形数据用于可视化
  metadata: json(), // 其他元数据
  download_count: integer().notNull().default(0),
  is_public: boolean().notNull().default(false),
  // Watermark and premium fields
  is_premium: boolean().notNull().default(false),
  has_watermark: boolean().notNull().default(true),
  original_file_url: varchar({ length: 500 }), // Original unwatermarked file URL
  created_at: timestamp({ withTimezone: true }).defaultNow(),
  updated_at: timestamp({ withTimezone: true }),
});

// Loop Verifications table - 循环校验结果
export const loopVerifications = pgTable("loop_verifications", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  track_uuid: varchar({ length: 255 }).notNull(),
  verification_score: decimal({ precision: 3, scale: 2 }), // 0.00 - 1.00
  is_seamless: boolean().notNull().default(false),
  start_analysis: json(), // 开头音频特征分析
  end_analysis: json(), // 结尾音频特征分析
  verification_method: varchar({ length: 50 }).notNull(), // ml, signal_processing
  created_at: timestamp({ withTimezone: true }).defaultNow(),
});

// Track Variations table - 音乐变奏
export const trackVariations = pgTable("track_variations", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  original_track_uuid: varchar({ length: 255 }).notNull(),
  user_uuid: varchar({ length: 255 }).notNull(),
  variation_type: varchar({ length: 50 }).notNull(), // tempo, style, mood
  variation_params: json(), // 变奏参数
  file_url: varchar({ length: 500 }).notNull(),
  file_size: integer(),
  file_format: varchar({ length: 10 }).notNull().default("mp3"),
  created_at: timestamp({ withTimezone: true }).defaultNow(),
});

// Track Stems table - 分轨信息
export const trackStems = pgTable("track_stems", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  track_uuid: varchar({ length: 255 }).notNull(),
  stem_type: varchar({ length: 50 }).notNull(), // drums, bass, melody, harmony, vocals
  file_url: varchar({ length: 500 }).notNull(),
  file_size: integer(),
  file_format: varchar({ length: 10 }).notNull().default("wav"),
  created_at: timestamp({ withTimezone: true }).defaultNow(),
});

// User Track Collections table - 用户音乐收藏
export const userTrackCollections = pgTable("user_track_collections", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  uuid: varchar({ length: 255 }).notNull().unique(),
  user_uuid: varchar({ length: 255 }).notNull(),
  name: varchar({ length: 255 }).notNull(),
  description: text(),
  is_public: boolean().notNull().default(false),
  created_at: timestamp({ withTimezone: true }).defaultNow(),
  updated_at: timestamp({ withTimezone: true }),
});

// Collection Tracks table - 收藏夹中的音乐
export const collectionTracks = pgTable("collection_tracks", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  collection_uuid: varchar({ length: 255 }).notNull(),
  track_uuid: varchar({ length: 255 }).notNull(),
  added_at: timestamp({ withTimezone: true }).defaultNow(),
});
